<?php $__env->startSection('page-title', 'Gestione Pagamenti'); ?>

<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        Gestione Pagamenti
     <?php $__env->endSlot(); ?>

    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <!-- Header with Filters and Add Button -->
        <div class="p-6 border-b border-gray-200">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-xl font-semibold text-gray-900">Lista Pagamenti</h2>
                <a href="<?php echo e(route('payments.create')); ?>" 
                   class="text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 hover:opacity-90"
                   style="background-color: #007BCE;"
                   onmouseover="this.style.backgroundColor='#005B99'"
                   onmouseout="this.style.backgroundColor='#007BCE'">
                    <i class="fas fa-plus mr-2"></i>
                    Aggiungi Pagamento
                </a>
            </div>

            <!-- Filters -->
            <form method="GET" class="flex flex-wrap gap-4">
                <div>
                    <select name="status" class="select-improved px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">Tutti gli stati</option>
                        <option value="pending" <?php echo e(request('status') === 'pending' ? 'selected' : ''); ?>>In Attesa</option>
                        <option value="overdue" <?php echo e(request('status') === 'overdue' ? 'selected' : ''); ?>>Scaduto</option>
                        <option value="completed" <?php echo e(request('status') === 'completed' ? 'selected' : ''); ?>>Completato</option>
                    </select>
                </div>
                <div>
                    <select name="month" class="select-improved px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">Tutti i mesi</option>
                        <?php for($i = 1; $i <= 12; $i++): ?>
                            <option value="<?php echo e($i); ?>" <?php echo e(request('month') == $i ? 'selected' : ''); ?>>
                                <?php echo e(DateTime::createFromFormat('!m', $i)->format('F')); ?>

                            </option>
                        <?php endfor; ?>
                    </select>
                </div>
                <div>
                    <select name="year" class="select-improved px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">Tutti gli anni</option>
                        <?php for($year = date('Y'); $year >= date('Y') - 5; $year--): ?>
                            <option value="<?php echo e($year); ?>" <?php echo e(request('year') == $year ? 'selected' : ''); ?>><?php echo e($year); ?></option>
                        <?php endfor; ?>
                    </select>
                </div>
                <button type="submit" class="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600">
                    Filtra
                </button>
                <a href="<?php echo e(route('payments.index')); ?>" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400">
                    Reset
                </a>
            </form>
        </div>

        <!-- Payments Table -->
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Progetto
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Cliente
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Importo
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Scadenza
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Stato
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Tipo
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Azioni
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php $__empty_1 = true; $__currentLoopData = $payments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $payment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr class="hover:bg-gray-50 <?php echo e($payment->isOverdue() ? 'bg-red-50' : ''); ?>">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">
                                    <?php echo e($payment->project->name); ?>

                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900"><?php echo e($payment->client->full_name); ?></div>
                                <div class="text-sm text-gray-500"><?php echo e($payment->client->email); ?></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">€<?php echo e(number_format($payment->amount, 2)); ?></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900"><?php echo e($payment->due_date->format('d/m/Y')); ?></div>
                                <?php if($payment->paid_date): ?>
                                    <div class="text-sm text-green-600">Pagato: <?php echo e($payment->paid_date->format('d/m/Y')); ?></div>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-medium rounded-full
                                    <?php if($payment->status === 'completed'): ?> bg-green-100 text-green-800
                                    <?php elseif($payment->status === 'overdue' || $payment->isOverdue()): ?> bg-red-100 text-red-800
                                    <?php else: ?> bg-yellow-100 text-yellow-800 <?php endif; ?>">
                                    <?php if($payment->isOverdue() && $payment->status === 'pending'): ?>
                                        Scaduto
                                    <?php else: ?>
                                        <?php echo e(App\Models\Payment::getStatuses()[$payment->status] ?? $payment->status); ?>

                                    <?php endif; ?>
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
                                    <?php echo e(App\Models\Payment::getPaymentTypes()[$payment->payment_type] ?? $payment->payment_type); ?>

                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <div class="flex justify-end space-x-2">
                                    <?php if($payment->status === 'completed'): ?>
                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('generate invoices')): ?>
                                        <a href="<?php echo e(route('payments.invoice', $payment)); ?>"
                                           class="inline-flex items-center px-2 py-1 text-xs text-white rounded transition-colors duration-200"
                                           style="background-color: #28a745;"
                                           onmouseover="this.style.backgroundColor='#218838'"
                                           onmouseout="this.style.backgroundColor='#28a745'"
                                           title="Scarica Fattura PDF">
                                            <i class="fas fa-file-pdf"></i>
                                        </a>
                                        <?php endif; ?>
                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('send emails')): ?>
                                        <form action="<?php echo e(route('payments.send-invoice', $payment)); ?>" method="POST" class="inline">
                                            <?php echo csrf_field(); ?>
                                            <button type="submit"
                                                    class="inline-flex items-center px-2 py-1 text-xs text-white rounded transition-colors duration-200"
                                                    style="background-color: #007BCE;"
                                                    onmouseover="this.style.backgroundColor='#005B99'"
                                                    onmouseout="this.style.backgroundColor='#007BCE'"
                                                    title="Invia Fattura via Email">
                                                <i class="fas fa-envelope"></i>
                                            </button>
                                        </form>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                    <?php if($payment->status === 'pending'): ?>
                                        <form action="<?php echo e(route('payments.mark-completed', $payment)); ?>"
                                              method="POST"
                                              class="inline">
                                            <?php echo csrf_field(); ?>
                                            <?php echo method_field('PATCH'); ?>
                                            <button type="submit"
                                                    class="text-green-600 hover:text-green-900"
                                                    title="Segna come completato">
                                                <i class="fas fa-check"></i>
                                            </button>
                                        </form>
                                    <?php endif; ?>
                                    <a href="<?php echo e(route('payments.show', $payment)); ?>"
                                       class="text-blue-600 hover:text-blue-900"
                                       title="Visualizza dettagli">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="<?php echo e(route('payments.edit', $payment)); ?>"
                                       class="text-yellow-600 hover:text-yellow-900"
                                       title="Modifica">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form action="<?php echo e(route('payments.destroy', $payment)); ?>"
                                          method="POST"
                                          class="inline"
                                          onsubmit="return confirm('Sei sicuro di voler eliminare questo pagamento?')">
                                        <?php echo csrf_field(); ?>
                                        <?php echo method_field('DELETE'); ?>
                                        <button type="submit"
                                                class="text-red-600 hover:text-red-900"
                                                title="Elimina">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="7" class="px-6 py-12 text-center text-gray-500">
                                <i class="fas fa-credit-card text-4xl mb-4 text-gray-300"></i>
                                <p class="text-lg">Nessun pagamento trovato</p>
                                <p class="text-sm">Inizia aggiungendo il tuo primo pagamento</p>
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <?php if($payments->hasPages()): ?>
            <div class="px-6 py-4 border-t border-gray-200">
                <?php echo e($payments->appends(request()->query())->links()); ?>

            </div>
        <?php endif; ?>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Desktop\Programmarti Gestionale\programmarti-gest\resources\views/payments/index.blade.php ENDPATH**/ ?>