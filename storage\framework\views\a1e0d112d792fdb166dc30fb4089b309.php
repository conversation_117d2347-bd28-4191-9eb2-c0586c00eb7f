<?php $__env->startSection('page-title', 'Dettagli Utente'); ?>

<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        Dettagli Utente: <?php echo e($user->full_name); ?>

     <?php $__env->endSlot(); ?>

    <div class="space-y-6">
        <!-- User Information Card -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="p-6 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h2 class="text-xl font-semibold text-gray-900">Informazioni Utente</h2>
                    <div class="flex space-x-2">
                        <a href="<?php echo e(route('users.edit', $user)); ?>" 
                           class="px-4 py-2 bg-yellow-500 hover:bg-yellow-600 text-white rounded-lg transition-colors duration-200">
                            <i class="fas fa-edit mr-2"></i>
                            Modifica
                        </a>
                        <a href="<?php echo e(route('users.index')); ?>" 
                           class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors duration-200">
                            <i class="fas fa-arrow-left mr-2"></i>
                            Torna alla Lista
                        </a>
                    </div>
                </div>
            </div>

            <div class="p-6">
                <div class="flex items-start space-x-6">
                    <!-- Profile Photo -->
                    <div class="flex-shrink-0">
                        <div class="w-24 h-24 rounded-full flex items-center justify-center" style="background-color: #007BCE;">
                            <?php if($user->profile_photo): ?>
                                <img src="<?php echo e($user->profile_photo_url); ?>" 
                                     alt="Profile" 
                                     class="w-full h-full rounded-full object-cover">
                            <?php else: ?>
                                <i class="fas fa-user text-white text-3xl"></i>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- User Details -->
                    <div class="flex-1 grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-500">Nome Completo</label>
                                <p class="text-lg font-medium text-gray-900"><?php echo e($user->full_name); ?></p>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-500">Username</label>
                                <p class="text-gray-900"><?php echo e($user->username); ?></p>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-500">Email</label>
                                <p class="text-gray-900">
                                    <a href="mailto:<?php echo e($user->email); ?>" style="color: #007BCE;" class="hover:opacity-80">
                                        <?php echo e($user->email); ?>

                                    </a>
                                </p>
                            </div>
                        </div>

                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-500">Ruolo</label>
                                <?php $__currentLoopData = $user->roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <span class="inline-flex px-3 py-1 text-sm font-medium rounded-full bg-purple-100 text-purple-800">
                                        <?php echo e($role->name); ?>

                                    </span>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-500">Data Registrazione</label>
                                <p class="text-gray-900"><?php echo e($user->created_at->format('d/m/Y H:i')); ?></p>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-500">Ultimo Aggiornamento</label>
                                <p class="text-gray-900"><?php echo e($user->updated_at->format('d/m/Y H:i')); ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Projects Section -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Progetti Assegnati</h3>
            </div>

            <div class="p-6">
                <?php if($user->projects->count() > 0): ?>
                    <div class="space-y-4">
                        <?php $__currentLoopData = $user->projects; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $project): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="border border-gray-200 rounded-lg p-4">
                                <div class="flex justify-between items-start">
                                    <div>
                                        <h4 class="font-medium text-gray-900"><?php echo e($project->name); ?></h4>
                                        <p class="text-sm text-gray-600 mt-1"><?php echo e($project->description); ?></p>
                                        <div class="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                                            <span>Cliente: <?php echo e($project->client->full_name); ?></span>
                                            <span>Inizio: <?php echo e($project->start_date->format('d/m/Y')); ?></span>
                                            <?php if($project->total_cost): ?>
                                                <span>Costo: €<?php echo e(number_format($project->total_cost, 2)); ?></span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <span class="px-2 py-1 text-xs font-medium rounded-full
                                        <?php if($project->status === 'completed'): ?> bg-green-100 text-green-800
                                        <?php elseif($project->status === 'in_progress'): ?> bg-blue-100 text-blue-800
                                        <?php elseif($project->status === 'planning'): ?> bg-yellow-100 text-yellow-800
                                        <?php else: ?> bg-gray-100 text-gray-800 <?php endif; ?>">
                                        <?php echo e(App\Models\Project::getStatuses()[$project->status] ?? $project->status); ?>

                                    </span>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php else: ?>
                    <div class="text-center py-8">
                        <i class="fas fa-project-diagram text-4xl text-gray-300 mb-4"></i>
                        <p class="text-gray-500">Nessun progetto assegnato a questo utente</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Payments Section -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Pagamenti Gestiti</h3>
            </div>

            <div class="p-6">
                <?php if($user->payments->count() > 0): ?>
                    <div class="space-y-4">
                        <?php $__currentLoopData = $user->payments->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $payment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="border border-gray-200 rounded-lg p-4">
                                <div class="flex justify-between items-center">
                                    <div>
                                        <h4 class="font-medium text-gray-900"><?php echo e($payment->project->name); ?></h4>
                                        <p class="text-sm text-gray-600">
                                            Cliente: <?php echo e($payment->client->full_name); ?> | 
                                            Scadenza: <?php echo e($payment->due_date->format('d/m/Y')); ?>

                                        </p>
                                    </div>
                                    <div class="text-right">
                                        <p class="font-medium text-gray-900">€<?php echo e(number_format($payment->amount, 2)); ?></p>
                                        <span class="px-2 py-1 text-xs font-medium rounded-full
                                            <?php if($payment->status === 'completed'): ?> bg-green-100 text-green-800
                                            <?php elseif($payment->status === 'overdue'): ?> bg-red-100 text-red-800
                                            <?php else: ?> bg-yellow-100 text-yellow-800 <?php endif; ?>">
                                            <?php echo e(App\Models\Payment::getStatuses()[$payment->status] ?? $payment->status); ?>

                                        </span>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php if($user->payments->count() > 5): ?>
                            <div class="text-center">
                                <a href="<?php echo e(route('payments.index', ['user_id' => $user->id])); ?>" 
                                   class="text-blue-600 hover:text-blue-800">
                                    Vedi tutti i pagamenti (<?php echo e($user->payments->count()); ?>)
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php else: ?>
                    <div class="text-center py-8">
                        <i class="fas fa-credit-card text-4xl text-gray-300 mb-4"></i>
                        <p class="text-gray-500">Nessun pagamento gestito da questo utente</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Expenses Section -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Spese Sostenute</h3>
            </div>

            <div class="p-6">
                <?php if($user->expenses->count() > 0): ?>
                    <div class="space-y-4">
                        <?php $__currentLoopData = $user->expenses->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $expense): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="border border-gray-200 rounded-lg p-4">
                                <div class="flex justify-between items-center">
                                    <div>
                                        <h4 class="font-medium text-gray-900"><?php echo e($expense->description); ?></h4>
                                        <p class="text-sm text-gray-600">
                                            Data: <?php echo e($expense->expense_date->format('d/m/Y')); ?>

                                            <?php if($expense->category): ?>
                                                | Categoria: <?php echo e(App\Models\Expense::getCategories()[$expense->category] ?? $expense->category); ?>

                                            <?php endif; ?>
                                        </p>
                                    </div>
                                    <div class="text-right">
                                        <p class="font-medium text-gray-900">€<?php echo e(number_format($expense->amount, 2)); ?></p>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php if($user->expenses->count() > 5): ?>
                            <div class="text-center">
                                <a href="<?php echo e(route('expenses.index', ['user_id' => $user->id])); ?>" 
                                   class="text-blue-600 hover:text-blue-800">
                                    Vedi tutte le spese (<?php echo e($user->expenses->count()); ?>)
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="mt-4 pt-4 border-t border-gray-200">
                        <p class="text-sm text-gray-600">
                            Totale spese: <strong>€<?php echo e(number_format($user->expenses->sum('amount'), 2)); ?></strong>
                        </p>
                    </div>
                <?php else: ?>
                    <div class="text-center py-8">
                        <i class="fas fa-receipt text-4xl text-gray-300 mb-4"></i>
                        <p class="text-gray-500">Nessuna spesa sostenuta da questo utente</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Desktop\Programmarti Gestionale\programmarti-gest\resources\views/users/show.blade.php ENDPATH**/ ?>